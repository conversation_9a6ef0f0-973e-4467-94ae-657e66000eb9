<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tech TRUV - Digital Solutions & Innovation</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
        
        * {
            font-family: 'Inter', sans-serif;
        }
        
        html {
            scroll-behavior: smooth;
        }
        
        .hero-bg {
            background: linear-gradient(135deg, #f8fafc 0%, #ffffff 50%, #f1f5f9 100%);
            position: relative;
        }
        
        .hero-bg::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                radial-gradient(circle at 20% 30%, rgba(99, 102, 241, 0.05) 0%, transparent 50%),
                radial-gradient(circle at 80% 70%, rgba(139, 92, 246, 0.05) 0%, transparent 50%),
                radial-gradient(circle at 60% 20%, rgba(59, 130, 246, 0.03) 0%, transparent 50%);
        }
        
        .floating {
            animation: float 8s ease-in-out infinite;
        }
        
        .floating:nth-child(2) {
            animation-delay: -2s;
        }
        
        .floating:nth-child(3) {
            animation-delay: -4s;
        }
        
        .floating:nth-child(4) {
            animation-delay: -6s;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            25% { transform: translateY(-15px) rotate(2deg); }
            50% { transform: translateY(-25px) rotate(0deg); }
            75% { transform: translateY(-10px) rotate(-2deg); }
        }
        
        .pulse-glow {
            animation: pulseGlow 3s ease-in-out infinite;
        }
        
        @keyframes pulseGlow {
            0%, 100% { box-shadow: 0 0 20px rgba(99, 102, 241, 0.3); }
            50% { box-shadow: 0 0 40px rgba(99, 102, 241, 0.6), 0 0 60px rgba(139, 92, 246, 0.3); }
        }
        
        .card-hover {
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            transform-style: preserve-3d;
        }
        
        .card-hover:hover {
            transform: translateY(-15px) rotateX(5deg);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.08), 0 15px 35px rgba(99, 102, 241, 0.1);
        }
        
        .gradient-text {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 50%, #06b6d4 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .gradient-border {
            background: linear-gradient(135deg, #4f46e5, #7c3aed, #06b6d4);
            padding: 2px;
            border-radius: 1rem;
        }
        
        .gradient-border-content {
            background: white;
            border-radius: calc(1rem - 2px);
        }
        
        .tech-pattern {
            background-image: 
                radial-gradient(circle at 25% 25%, rgba(99, 102, 241, 0.02) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(139, 92, 246, 0.02) 0%, transparent 50%);
            background-size: 100px 100px;
        }
        
        .navbar-glass {
            backdrop-filter: blur(20px);
            background: rgba(255, 255, 255, 0.9);
            border-bottom: 1px solid rgba(99, 102, 241, 0.1);
        }
        
        .morphism {
            background: rgba(255, 255, 255, 0.7);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.06);
        }
        
        .text-reveal {
            opacity: 0;
            transform: translateY(30px);
            animation: textReveal 0.8s ease forwards;
        }
        
        @keyframes textReveal {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .slide-in-left {
            opacity: 0;
            transform: translateX(-50px);
            animation: slideInLeft 0.8s ease forwards;
        }
        
        .slide-in-right {
            opacity: 0;
            transform: translateX(50px);
            animation: slideInRight 0.8s ease forwards;
        }
        
        @keyframes slideInLeft {
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }
        
        @keyframes slideInRight {
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }
        
        .scale-in {
            opacity: 0;
            transform: scale(0.8);
            animation: scaleIn 0.6s ease forwards;
        }
        
        @keyframes scaleIn {
            to {
                opacity: 1;
                transform: scale(1);
            }
        }
        
        .interactive-button {
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        
        .interactive-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.5s ease;
        }
        
        .interactive-button:hover::before {
            left: 100%;
        }
        
        .service-icon {
            transition: all 0.3s ease;
        }
        
        .service-card:hover .service-icon {
            transform: scale(1.1) rotate(5deg);
        }
        
        .stats-counter {
            font-variant-numeric: tabular-nums;
        }
        
        .testimonial-card {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.9) 100%);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(99, 102, 241, 0.1);
        }
        
        .portfolio-overlay {
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.9) 0%, rgba(139, 92, 246, 0.9) 100%);
            opacity: 0;
            transition: all 0.3s ease;
        }
        
        .portfolio-card:hover .portfolio-overlay {
            opacity: 1;
        }
        
        .contact-form {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
            backdrop-filter: blur(20px);
        }
        
        .input-focus {
            transition: all 0.3s ease;
        }
        
        .input-focus:focus {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(99, 102, 241, 0.1);
        }
        
        .scroll-indicator {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #4f46e5, #7c3aed, #06b6d4);
            transform-origin: left;
            transform: scaleX(0);
            z-index: 9999;
        }
        
        .parallax {
            transform: translateZ(0);
            will-change: transform;
        }
        
        .typing-animation::after {
            content: '|';
            animation: blink 1s infinite;
        }
        
        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }
    </style>
</head>
<body class="bg-white">
    <!-- Scroll Progress Indicator -->
    <div class="scroll-indicator" id="scrollIndicator"></div>

    <!-- Navigation -->
    <nav class="fixed top-0 w-full z-50 navbar-glass">
        <div class="container mx-auto px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="text-3xl font-black gradient-text scale-in">Tech TRUV</div>
                <div class="hidden md:flex space-x-8">
                    <a href="#home" class="text-gray-700 hover:text-indigo-600 transition-all duration-300 font-medium relative group">
                        Home
                        <span class="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-indigo-600 to-purple-600 transition-all duration-300 group-hover:w-full"></span>
                    </a>
                    <a href="#services" class="text-gray-700 hover:text-indigo-600 transition-all duration-300 font-medium relative group">
                        Services
                        <span class="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-indigo-600 to-purple-600 transition-all duration-300 group-hover:w-full"></span>
                    </a>
                    <a href="#about" class="text-gray-700 hover:text-indigo-600 transition-all duration-300 font-medium relative group">
                        About
                        <span class="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-indigo-600 to-purple-600 transition-all duration-300 group-hover:w-full"></span>
                    </a>
                    <a href="#portfolio" class="text-gray-700 hover:text-indigo-600 transition-all duration-300 font-medium relative group">
                        Portfolio
                        <span class="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-indigo-600 to-purple-600 transition-all duration-300 group-hover:w-full"></span>
                    </a>
                    <a href="#contact" class="text-gray-700 hover:text-indigo-600 transition-all duration-300 font-medium relative group">
                        Contact
                        <span class="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-indigo-600 to-purple-600 transition-all duration-300 group-hover:w-full"></span>
                    </a>
                </div>
                <button class="interactive-button bg-gradient-to-r from-indigo-600 to-purple-600 text-white px-8 py-3 rounded-full hover:from-indigo-700 hover:to-purple-700 transition-all duration-300 transform hover:scale-105 pulse-glow font-semibold">
                    Get Started
                </button>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero-bg min-h-screen flex items-center relative overflow-hidden tech-pattern">
        <div class="container mx-auto px-6 relative z-10">
            <div class="max-w-6xl mx-auto">
                <div class="grid lg:grid-cols-2 gap-12 items-center">
                    <div class="text-center lg:text-left">
                        <div class="text-reveal">
                            <h1 class="text-7xl md:text-8xl font-black mb-6 leading-tight">
                                <span class="gradient-text">Tech</span>
                                <span class="text-gray-800"> TRUV</span>
                            </h1>
                        </div>
                        <div class="text-reveal" style="animation-delay: 0.2s;">
                            <h2 class="text-3xl md:text-4xl font-light mb-8 text-gray-600">
                                Your Digital Growth Partner
                            </h2>
                        </div>
                        <div class="text-reveal" style="animation-delay: 0.4s;">
                            <p class="text-xl md:text-2xl text-gray-500 mb-12 max-w-2xl leading-relaxed">
                                Transforming businesses through <span class="gradient-text font-semibold">innovative technology solutions</span>, strategic partnerships, and cutting-edge digital experiences.
                            </p>
                        </div>
                        <div class="text-reveal flex flex-col sm:flex-row gap-6 justify-center lg:justify-start" style="animation-delay: 0.6s;">
                            <button class="interactive-button bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white px-10 py-4 rounded-full text-lg font-semibold transition-all duration-300 transform hover:scale-105 pulse-glow">
                                Start Your Journey
                            </button>
                            <button class="interactive-button border-2 border-gray-300 hover:border-indigo-500 text-gray-700 hover:text-indigo-600 px-10 py-4 rounded-full text-lg font-semibold transition-all duration-300 bg-white/50 backdrop-blur-sm">
                                Learn More
                            </button>
                        </div>
                    </div>
                    
                    <div class="relative scale-in" style="animation-delay: 0.8s;">
                        <div class="relative w-full h-96 flex items-center justify-center">
                            <!-- Central Tech Hub -->
                            <div class="absolute w-32 h-32 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-3xl flex items-center justify-center text-white text-4xl font-bold shadow-2xl pulse-glow">
                                TT
                            </div>
                            
                            <!-- Orbiting Elements -->
                            <div class="floating absolute w-16 h-16 bg-gradient-to-br from-blue-400 to-cyan-400 rounded-2xl flex items-center justify-center text-white text-xl shadow-lg" style="top: 20px; left: 50px;">
                                💻
                            </div>
                            <div class="floating absolute w-14 h-14 bg-gradient-to-br from-purple-400 to-pink-400 rounded-xl flex items-center justify-center text-white text-lg shadow-lg" style="top: 60px; right: 40px;">
                                🤖
                            </div>
                            <div class="floating absolute w-18 h-18 bg-gradient-to-br from-teal-400 to-green-400 rounded-2xl flex items-center justify-center text-white text-xl shadow-lg" style="bottom: 80px; left: 30px;">
                                📈
                            </div>
                            <div class="floating absolute w-12 h-12 bg-gradient-to-br from-orange-400 to-red-400 rounded-lg flex items-center justify-center text-white shadow-lg" style="bottom: 40px; right: 60px;">
                                ⚡
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section id="services" class="py-24 bg-gradient-to-b from-white to-gray-50">
        <div class="container mx-auto px-6">
            <div class="text-center mb-20">
                <div class="text-reveal">
                    <h2 class="text-5xl md:text-6xl font-black gradient-text mb-8">Our Services</h2>
                </div>
                <div class="text-reveal" style="animation-delay: 0.2s;">
                    <p class="text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
                        Comprehensive digital solutions designed to <span class="gradient-text font-semibold">accelerate your business growth</span> and digital transformation.
                    </p>
                </div>
            </div>
            
            <div class="grid md:grid-cols-3 gap-10">
                <!-- Web Development -->
                <div class="service-card card-hover gradient-border scale-in" style="animation-delay: 0.1s;">
                    <div class="gradient-border-content p-10 h-full">
                        <div class="service-icon w-20 h-20 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-3xl flex items-center justify-center text-white text-3xl mb-8 mx-auto">
                            💻
                        </div>
                        <h3 class="text-3xl font-bold mb-6 text-gray-800 text-center">Website & Software</h3>
                        <p class="text-gray-600 mb-8 leading-relaxed text-center text-lg">
                            Custom websites, web applications, and software solutions built with cutting-edge technologies for optimal performance.
                        </p>
                        <ul class="space-y-4 mb-8">
                            <li class="flex items-center text-gray-600">
                                <span class="w-3 h-3 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full mr-4"></span>
                                <span class="font-medium">Custom Websites</span>
                            </li>
                            <li class="flex items-center text-gray-600">
                                <span class="w-3 h-3 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full mr-4"></span>
                                <span class="font-medium">Software Development</span>
                            </li>
                            <li class="flex items-center text-gray-600">
                                <span class="w-3 h-3 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full mr-4"></span>
                                <span class="font-medium">Web Hosting</span>
                            </li>
                        </ul>
                        <button class="interactive-button w-full bg-gradient-to-r from-blue-500 to-cyan-500 text-white py-4 rounded-2xl font-semibold hover:from-blue-600 hover:to-cyan-600 transition-all duration-300 transform hover:scale-105">
                            Learn More
                        </button>
                    </div>
                </div>
                
                <!-- AI & Automation -->
                <div class="service-card card-hover gradient-border scale-in" style="animation-delay: 0.3s;">
                    <div class="gradient-border-content p-10 h-full">
                        <div class="service-icon w-20 h-20 bg-gradient-to-br from-purple-500 to-pink-500 rounded-3xl flex items-center justify-center text-white text-3xl mb-8 mx-auto">
                            🤖
                        </div>
                        <h3 class="text-3xl font-bold mb-6 text-gray-800 text-center">Apps & AI Agents</h3>
                        <p class="text-gray-600 mb-8 leading-relaxed text-center text-lg">
                            Mobile applications and AI-powered automation agents integrated into websites and software for enhanced functionality.
                        </p>
                        <ul class="space-y-4 mb-8">
                            <li class="flex items-center text-gray-600">
                                <span class="w-3 h-3 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full mr-4"></span>
                                <span class="font-medium">Mobile Apps</span>
                            </li>
                            <li class="flex items-center text-gray-600">
                                <span class="w-3 h-3 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full mr-4"></span>
                                <span class="font-medium">AI Agent Integration</span>
                            </li>
                            <li class="flex items-center text-gray-600">
                                <span class="w-3 h-3 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full mr-4"></span>
                                <span class="font-medium">Smart Automation</span>
                            </li>
                        </ul>
                        <button class="interactive-button w-full bg-gradient-to-r from-purple-500 to-pink-500 text-white py-4 rounded-2xl font-semibold hover:from-purple-600 hover:to-pink-600 transition-all duration-300 transform hover:scale-105">
                            Learn More
                        </button>
                    </div>
                </div>
                
                <!-- Digital Strategy -->
                <div class="service-card card-hover gradient-border scale-in" style="animation-delay: 0.5s;">
                    <div class="gradient-border-content p-10 h-full">
                        <div class="service-icon w-20 h-20 bg-gradient-to-br from-teal-500 to-green-500 rounded-3xl flex items-center justify-center text-white text-3xl mb-8 mx-auto">
                            📈
                        </div>
                        <h3 class="text-3xl font-bold mb-6 text-gray-800 text-center">Business & Training</h3>
                        <p class="text-gray-600 mb-8 leading-relaxed text-center text-lg">
                            Business consulting services, professional internship programs, and comprehensive training to accelerate growth.
                        </p>
                        <ul class="space-y-4 mb-8">
                            <li class="flex items-center text-gray-600">
                                <span class="w-3 h-3 bg-gradient-to-r from-teal-500 to-green-500 rounded-full mr-4"></span>
                                <span class="font-medium">Business Consulting</span>
                            </li>
                            <li class="flex items-center text-gray-600">
                                <span class="w-3 h-3 bg-gradient-to-r from-teal-500 to-green-500 rounded-full mr-4"></span>
                                <span class="font-medium">Internship Programs</span>
                            </li>
                            <li class="flex items-center text-gray-600">
                                <span class="w-3 h-3 bg-gradient-to-r from-teal-500 to-green-500 rounded-full mr-4"></span>
                                <span class="font-medium">Professional Training</span>
                            </li>
                        </ul>
                        <button class="interactive-button w-full bg-gradient-to-r from-teal-500 to-green-500 text-white py-4 rounded-2xl font-semibold hover:from-teal-600 hover:to-green-600 transition-all duration-300 transform hover:scale-105">
                            Learn More
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="py-24 bg-white tech-pattern">
        <div class="container mx-auto px-6">
            <div class="grid lg:grid-cols-2 gap-16 items-center">
                <div class="slide-in-left">
                    <h2 class="text-5xl md:text-6xl font-black gradient-text mb-8">About Tech TRUV</h2>
                    <p class="text-2xl text-gray-600 mb-10 leading-relaxed">
                        We're a team of passionate <span class="gradient-text font-semibold">technologists, designers, and strategists</span> dedicated to helping businesses unlock their digital potential.
                    </p>
                    <div class="space-y-8">
                        <div class="flex items-start space-x-6">
                            <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-500 rounded-2xl flex items-center justify-center text-white font-bold text-lg flex-shrink-0">
                                1
                            </div>
                            <div>
                                <h3 class="text-2xl font-bold text-gray-800 mb-3">Innovation First</h3>
                                <p class="text-gray-600 text-lg leading-relaxed">We stay ahead of technology trends to deliver cutting-edge solutions that give you a competitive advantage.</p>
                            </div>
                        </div>
                        <div class="flex items-start space-x-6">
                            <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center text-white font-bold text-lg flex-shrink-0">
                                2
                            </div>
                            <div>
                                <h3 class="text-2xl font-bold text-gray-800 mb-3">Client Success</h3>
                                <p class="text-gray-600 text-lg leading-relaxed">Your success is our success. We're committed to delivering results that matter and drive real business growth.</p>
                            </div>
                        </div>
                        <div class="flex items-start space-x-6">
                            <div class="w-12 h-12 bg-gradient-to-br from-teal-500 to-green-500 rounded-2xl flex items-center justify-center text-white font-bold text-lg flex-shrink-0">
                                3
                            </div>
                            <div>
                                <h3 class="text-2xl font-bold text-gray-800 mb-3">Quality Driven</h3>
                                <p class="text-gray-600 text-lg leading-relaxed">Every project is crafted with meticulous attention to detail and industry best practices.</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="slide-in-right">
                    <div class="morphism rounded-3xl p-10">
                        <h3 class="text-3xl font-bold gradient-text mb-8 text-center">Our Impact</h3>
                        <div class="grid grid-cols-2 gap-8">
                            <div class="text-center">
                                <div class="stats-counter text-5xl font-black gradient-text mb-3" data-target="250">0</div>
                                <div class="text-gray-600 font-medium text-lg">Projects Completed</div>
                            </div>
                            <div class="text-center">
                                <div class="stats-counter text-5xl font-black gradient-text mb-3" data-target="98">0</div>
                                <div class="text-gray-600 font-medium text-lg">Client Satisfaction</div>
                            </div>
                            <div class="text-center">
                                <div class="stats-counter text-5xl font-black gradient-text mb-3" data-target="7">0</div>
                                <div class="text-gray-600 font-medium text-lg">Years Experience</div>
                            </div>
                            <div class="text-center">
                                <div class="stats-counter text-5xl font-black gradient-text mb-3">24/7</div>
                                <div class="text-gray-600 font-medium text-lg">Support Available</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Portfolio Section -->
    <section id="portfolio" class="py-24 bg-gradient-to-b from-gray-50 to-white">
        <div class="container mx-auto px-6">
            <div class="text-center mb-20">
                <div class="text-reveal">
                    <h2 class="text-5xl md:text-6xl font-black gradient-text mb-8">Our Work</h2>
                </div>
                <div class="text-reveal" style="animation-delay: 0.2s;">
                    <p class="text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
                        Explore some of our recent projects and see how we've helped businesses <span class="gradient-text font-semibold">transform digitally</span>.
                    </p>
                </div>
            </div>
            
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-10">
                <!-- Project 1 -->
                <div class="portfolio-card card-hover morphism rounded-3xl overflow-hidden relative group scale-in" style="animation-delay: 0.1s;">
                    <div class="h-64 bg-gradient-to-br from-blue-500 to-cyan-500 flex items-center justify-center relative">
                        <div class="text-white text-7xl">🏢</div>
                        <div class="portfolio-overlay absolute inset-0 flex items-center justify-center">
                            <button class="interactive-button bg-white text-indigo-600 px-8 py-3 rounded-full font-semibold transform hover:scale-105">
                                View Project
                            </button>
                        </div>
                    </div>
                    <div class="p-8">
                        <h3 class="text-2xl font-bold mb-3 text-gray-800">Corporate Website</h3>
                        <p class="text-gray-600 mb-6 leading-relaxed">Modern corporate website with advanced analytics and CRM integration for enhanced business operations.</p>
                        <div class="flex flex-wrap gap-3 mb-6">
                            <span class="px-4 py-2 bg-blue-100 text-blue-600 rounded-full text-sm font-medium">React</span>
                            <span class="px-4 py-2 bg-green-100 text-green-600 rounded-full text-sm font-medium">Node.js</span>
                            <span class="px-4 py-2 bg-purple-100 text-purple-600 rounded-full text-sm font-medium">Analytics</span>
                        </div>
                    </div>
                </div>
                
                <!-- Project 2 -->
                <div class="portfolio-card card-hover morphism rounded-3xl overflow-hidden relative group scale-in" style="animation-delay: 0.3s;">
                    <div class="h-64 bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center relative">
                        <div class="text-white text-7xl">🛒</div>
                        <div class="portfolio-overlay absolute inset-0 flex items-center justify-center">
                            <button class="interactive-button bg-white text-purple-600 px-8 py-3 rounded-full font-semibold transform hover:scale-105">
                                View Project
                            </button>
                        </div>
                    </div>
                    <div class="p-8">
                        <h3 class="text-2xl font-bold mb-3 text-gray-800">E-commerce Platform</h3>
                        <p class="text-gray-600 mb-6 leading-relaxed">Full-featured online store with AI-powered recommendations and seamless payment integration.</p>
                        <div class="flex flex-wrap gap-3 mb-6">
                            <span class="px-4 py-2 bg-purple-100 text-purple-600 rounded-full text-sm font-medium">E-commerce</span>
                            <span class="px-4 py-2 bg-pink-100 text-pink-600 rounded-full text-sm font-medium">AI</span>
                            <span class="px-4 py-2 bg-blue-100 text-blue-600 rounded-full text-sm font-medium">Payment</span>
                        </div>
                    </div>
                </div>
                
                <!-- Project 3 -->
                <div class="portfolio-card card-hover morphism rounded-3xl overflow-hidden relative group scale-in" style="animation-delay: 0.5s;">
                    <div class="h-64 bg-gradient-to-br from-teal-500 to-green-500 flex items-center justify-center relative">
                        <div class="text-white text-7xl">📱</div>
                        <div class="portfolio-overlay absolute inset-0 flex items-center justify-center">
                            <button class="interactive-button bg-white text-teal-600 px-8 py-3 rounded-full font-semibold transform hover:scale-105">
                                View Project
                            </button>
                        </div>
                    </div>
                    <div class="p-8">
                        <h3 class="text-2xl font-bold mb-3 text-gray-800">Mobile App</h3>
                        <p class="text-gray-600 mb-6 leading-relaxed">Cross-platform mobile application with real-time features and intuitive user experience.</p>
                        <div class="flex flex-wrap gap-3 mb-6">
                            <span class="px-4 py-2 bg-teal-100 text-teal-600 rounded-full text-sm font-medium">Mobile</span>
                            <span class="px-4 py-2 bg-green-100 text-green-600 rounded-full text-sm font-medium">Real-time</span>
                            <span class="px-4 py-2 bg-blue-100 text-blue-600 rounded-full text-sm font-medium">Cross-platform</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section class="py-24 bg-white tech-pattern">
        <div class="container mx-auto px-6">
            <div class="text-center mb-20">
                <div class="text-reveal">
                    <h2 class="text-5xl md:text-6xl font-black gradient-text mb-8">What Our Clients Say</h2>
                </div>
                <div class="text-reveal" style="animation-delay: 0.2s;">
                    <p class="text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
                        Don't just take our word for it. Here's what our clients have to say about <span class="gradient-text font-semibold">working with Tech TRUV</span>.
                    </p>
                </div>
            </div>
            
            <div class="grid md:grid-cols-3 gap-10">
                <div class="testimonial-card rounded-3xl p-10 scale-in" style="animation-delay: 0.1s;">
                    <div class="flex items-center mb-8">
                        <div class="w-16 h-16 bg-gradient-to-br from-blue-400 to-cyan-400 rounded-full flex items-center justify-center text-white font-bold text-xl">
                            S
                        </div>
                        <div class="ml-6">
                            <div class="text-gray-800 font-bold text-lg">Sarah Johnson</div>
                            <div class="text-gray-500">CEO, TechStart Inc.</div>
                        </div>
                    </div>
                    <p class="text-gray-600 leading-relaxed text-lg mb-6">
                        "Tech TRUV transformed our digital presence completely. Their innovative approach and attention to detail exceeded our expectations."
                    </p>
                    <div class="flex text-yellow-400 text-xl">
                        ⭐⭐⭐⭐⭐
                    </div>
                </div>
                
                <div class="testimonial-card rounded-3xl p-10 scale-in" style="animation-delay: 0.3s;">
                    <div class="flex items-center mb-8">
                        <div class="w-16 h-16 bg-gradient-to-br from-purple-400 to-pink-400 rounded-full flex items-center justify-center text-white font-bold text-xl">
                            M
                        </div>
                        <div class="ml-6">
                            <div class="text-gray-800 font-bold text-lg">Michael Chen</div>
                            <div class="text-gray-500">Founder, GrowthLab</div>
                        </div>
                    </div>
                    <p class="text-gray-600 leading-relaxed text-lg mb-6">
                        "The AI automation solutions they built for us saved us 40+ hours per week. Incredible ROI and professional service."
                    </p>
                    <div class="flex text-yellow-400 text-xl">
                        ⭐⭐⭐⭐⭐
                    </div>
                </div>
                
                <div class="testimonial-card rounded-3xl p-10 scale-in" style="animation-delay: 0.5s;">
                    <div class="flex items-center mb-8">
                        <div class="w-16 h-16 bg-gradient-to-br from-teal-400 to-green-400 rounded-full flex items-center justify-center text-white font-bold text-xl">
                            E
                        </div>
                        <div class="ml-6">
                            <div class="text-gray-800 font-bold text-lg">Emily Rodriguez</div>
                            <div class="text-gray-500">Director, InnovateCorp</div>
                        </div>
                    </div>
                    <p class="text-gray-600 leading-relaxed text-lg mb-6">
                        "Working with Tech TRUV was a game-changer. They delivered a beautiful, functional website that drives real results."
                    </p>
                    <div class="flex text-yellow-400 text-xl">
                        ⭐⭐⭐⭐⭐
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="py-24 bg-gradient-to-b from-gray-50 to-white">
        <div class="container mx-auto px-6">
            <div class="max-w-6xl mx-auto">
                <div class="text-center mb-20">
                    <div class="text-reveal">
                        <h2 class="text-5xl md:text-6xl font-black gradient-text mb-8">Let's Work Together</h2>
                    </div>
                    <div class="text-reveal" style="animation-delay: 0.2s;">
                        <p class="text-2xl text-gray-600 leading-relaxed">
                            Ready to transform your business? <span class="gradient-text font-semibold">Get in touch</span> and let's discuss your project.
                        </p>
                    </div>
                </div>
                
                <div class="grid lg:grid-cols-2 gap-16">
                    <div class="slide-in-left">
                        <h3 class="text-3xl font-bold text-gray-800 mb-10">Get In Touch</h3>
                        <div class="space-y-8">
                            <div class="flex items-center space-x-6">
                                <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-2xl flex items-center justify-center text-white text-2xl">
                                    📧
                                </div>
                                <div>
                                    <div class="font-bold text-gray-800 text-xl">Email</div>
                                    <div class="text-gray-600 text-lg"><EMAIL></div>
                                </div>
                            </div>
                            <div class="flex items-center space-x-6">
                                <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center text-white text-2xl">
                                    📱
                                </div>
                                <div>
                                    <div class="font-bold text-gray-800 text-xl">Phone</div>
                                    <div class="text-gray-600 text-lg">+91 7970087577</div>
                                </div>
                            </div>
                            <div class="flex items-center space-x-6">
                                <div class="w-16 h-16 bg-gradient-to-br from-teal-500 to-green-500 rounded-2xl flex items-center justify-center text-white text-2xl">
                                    📍
                                </div>
                                <div>
                                    <div class="font-bold text-gray-800 text-xl">Office</div>
                                    <div class="text-gray-600 text-lg">Raipur, Chhattisgarh, 492001</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="slide-in-right">
                        <div class="contact-form rounded-3xl p-10 border border-gray-200">
                            <h3 class="text-2xl font-bold text-gray-800 mb-8">Send us a message</h3>
                            <div class="space-y-6">
                                <div>
                                    <label class="block text-sm font-semibold text-gray-700 mb-3">Name</label>
                                    <input type="text" class="input-focus w-full px-6 py-4 border border-gray-300 rounded-2xl focus:ring-2 focus:ring-indigo-500 focus:border-transparent text-lg" placeholder="Your name">
                                </div>
                                <div>
                                    <label class="block text-sm font-semibold text-gray-700 mb-3">Email</label>
                                    <input type="email" class="input-focus w-full px-6 py-4 border border-gray-300 rounded-2xl focus:ring-2 focus:ring-indigo-500 focus:border-transparent text-lg" placeholder="<EMAIL>">
                                </div>
                                <div>
                                    <label class="block text-sm font-semibold text-gray-700 mb-3">Project Type</label>
                                    <select class="input-focus w-full px-6 py-4 border border-gray-300 rounded-2xl focus:ring-2 focus:ring-indigo-500 focus:border-transparent text-lg">
                                        <option>Website Development</option>
                                        <option>Software Development</option>
                                        <option>Mobile App Development</option>
                                        <option>AI Agent & Automation</option>
                                        <option>Web Hosting</option>
                                        <option>Business Consulting</option>
                                        <option>Internship Program</option>
                                        <option>Training Services</option>
                                        <option>Other</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-semibold text-gray-700 mb-3">Message</label>
                                    <textarea rows="5" class="input-focus w-full px-6 py-4 border border-gray-300 rounded-2xl focus:ring-2 focus:ring-indigo-500 focus:border-transparent text-lg" placeholder="Tell us about your project..."></textarea>
                                </div>
                                <button class="interactive-button w-full bg-gradient-to-r from-indigo-600 to-purple-600 text-white py-4 rounded-2xl font-semibold hover:from-indigo-700 hover:to-purple-700 transition-all duration-300 transform hover:scale-105 text-lg">
                                    Send Message
                                </button>
                            </div>
                            <div class="mt-6 text-center">
                                <p class="text-sm text-gray-500">
                                    <span class="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full font-medium">Demo Form</span> - This is a sample contact form for demonstration purposes.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-16">
        <div class="container mx-auto px-6">
            <div class="grid md:grid-cols-4 gap-12">
                <div>
                    <div class="text-3xl font-black gradient-text mb-6">Tech TRUV</div>
                    <p class="text-gray-400 leading-relaxed text-lg">
                        Transforming businesses through innovative technology solutions and strategic digital partnerships.
                    </p>
                </div>
                <div>
                    <h3 class="text-xl font-bold mb-6">Services</h3>
                    <ul class="space-y-3 text-gray-400">
                        <li><a href="#" class="hover:text-white transition-colors text-lg">Website & Software</a></li>
                        <li><a href="#" class="hover:text-white transition-colors text-lg">Mobile Apps</a></li>
                        <li><a href="#" class="hover:text-white transition-colors text-lg">AI Agents & Automation</a></li>
                        <li><a href="#" class="hover:text-white transition-colors text-lg">Web Hosting</a></li>
                        <li><a href="#" class="hover:text-white transition-colors text-lg">Business Consulting</a></li>
                        <li><a href="#" class="hover:text-white transition-colors text-lg">Training & Internships</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-xl font-bold mb-6">Company</h3>
                    <ul class="space-y-3 text-gray-400">
                        <li><a href="#" class="hover:text-white transition-colors text-lg">About Us</a></li>
                        <li><a href="#" class="hover:text-white transition-colors text-lg">Portfolio</a></li>
                        <li><a href="#" class="hover:text-white transition-colors text-lg">Careers</a></li>
                        <li><a href="#" class="hover:text-white transition-colors text-lg">Contact</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-xl font-bold mb-6">Connect</h3>
                    <div class="flex space-x-4">
                        <a href="#" class="w-12 h-12 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-2xl flex items-center justify-center hover:scale-110 transition-transform text-xl">
                            📘
                        </a>
                        <a href="#" class="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center hover:scale-110 transition-transform text-xl">
                            📷
                        </a>
                        <a href="#" class="w-12 h-12 bg-gradient-to-br from-blue-400 to-blue-600 rounded-2xl flex items-center justify-center hover:scale-110 transition-transform text-xl">
                            🐦
                        </a>
                        <a href="#" class="w-12 h-12 bg-gradient-to-br from-blue-600 to-blue-800 rounded-2xl flex items-center justify-center hover:scale-110 transition-transform text-xl">
                            💼
                        </a>
                    </div>
                </div>
            </div>
            <div class="border-t border-gray-800 mt-12 pt-8 text-center text-gray-400">
                <p class="text-lg">&copy; 2024 Tech TRUV. All rights reserved. Built with innovation and passion.</p>
            </div>
        </div>
    </footer>

    <script>
        // Scroll Progress Indicator
        window.addEventListener('scroll', function() {
            const scrolled = (window.scrollY / (document.documentElement.scrollHeight - window.innerHeight)) * 100;
            document.getElementById('scrollIndicator').style.transform = `scaleX(${scrolled / 100})`;
        });

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Stats Counter Animation
        function animateCounter(element, target, duration = 2000) {
            let start = 0;
            const increment = target / (duration / 16);
            const timer = setInterval(() => {
                start += increment;
                if (start >= target) {
                    element.textContent = target + (target === 98 ? '%' : target === 7 ? '+' : '+');
                    clearInterval(timer);
                } else {
                    element.textContent = Math.floor(start) + (target === 98 ? '%' : target === 7 ? '+' : '+');
                }
            }, 16);
        }

        // Intersection Observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    // Stats counter animation
                    if (entry.target.classList.contains('stats-counter')) {
                        const target = parseInt(entry.target.getAttribute('data-target'));
                        if (target) {
                            animateCounter(entry.target, target);
                        }
                    }
                    
                    // General animations
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0) translateX(0) scale(1)';
                }
            });
        }, observerOptions);

        // Observe all animated elements
        document.querySelectorAll('.card-hover, .stats-counter, .text-reveal, .slide-in-left, .slide-in-right, .scale-in').forEach(element => {
            observer.observe(element);
        });

        // Form submission handler
        document.querySelector('button[type="submit"], .contact-form button')?.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Add loading state
            this.innerHTML = 'Sending...';
            this.disabled = true;
            
            // Simulate form submission
            setTimeout(() => {
                alert('Thank you for your message! This is a demo form. In a real website, this would send your message to Tech TRUV.');
                this.innerHTML = 'Send Message';
                this.disabled = false;
            }, 1500);
        });

        // Parallax effect for floating elements
        window.addEventListener('scroll', function() {
            const scrolled = window.pageYOffset;
            const parallaxElements = document.querySelectorAll('.floating');
            
            parallaxElements.forEach((element, index) => {
                const speed = 0.5 + (index * 0.1);
                element.style.transform = `translateY(${scrolled * speed}px)`;
            });
        });

        // Add navbar shadow on scroll
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('nav');
            if (window.scrollY > 100) {
                navbar.style.boxShadow = '0 10px 30px rgba(0, 0, 0, 0.1)';
            } else {
                navbar.style.boxShadow = 'none';
            }
        });

        // Interactive button effects
        document.querySelectorAll('.interactive-button').forEach(button => {
            button.addEventListener('mouseenter', function() {
                this.style.transform = 'scale(1.05) translateY(-2px)';
            });
            
            button.addEventListener('mouseleave', function() {
                this.style.transform = 'scale(1) translateY(0)';
            });
        });

        // Add typing animation to hero text
        function typeWriter(element, text, speed = 100) {
            let i = 0;
            element.innerHTML = '';
            function type() {
                if (i < text.length) {
                    element.innerHTML += text.charAt(i);
                    i++;
                    setTimeout(type, speed);
                }
            }
            type();
        }

        // Initialize animations on page load
        window.addEventListener('load', function() {
            // Add stagger delay to cards
            document.querySelectorAll('.card-hover').forEach((card, index) => {
                card.style.animationDelay = `${index * 0.1}s`;
            });
        });
    </script>
<script>(function(){function c(){var b=a.contentDocument||a.contentWindow.document;if(b){var d=b.createElement('script');d.innerHTML="window.__CF$cv$params={r:'97b52f7ce5ed3e27',t:'MTc1NzIzNzQwNi4wMDAwMDA='};var a=document.createElement('script');a.nonce='';a.src='/cdn-cgi/challenge-platform/scripts/jsd/main.js';document.getElementsByTagName('head')[0].appendChild(a);";b.getElementsByTagName('head')[0].appendChild(d)}}if(document.body){var a=document.createElement('iframe');a.height=1;a.width=1;a.style.position='absolute';a.style.top=0;a.style.left=0;a.style.border='none';a.style.visibility='hidden';document.body.appendChild(a);if('loading'!==document.readyState)c();else if(window.addEventListener)document.addEventListener('DOMContentLoaded',c);else{var e=document.onreadystatechange||function(){};document.onreadystatechange=function(b){e(b);'loading'!==document.readyState&&(document.onreadystatechange=e,c())}}}})();</script></body>
</html>
